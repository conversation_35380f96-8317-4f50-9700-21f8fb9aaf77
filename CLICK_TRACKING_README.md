# Email Click Tracking System

## Overview

This system provides comprehensive click tracking for email campaigns sent through the FastAPI email service. It tracks user interactions with CTA (Call-to-Action) buttons in both HORIZON6 and ENTRA6 email templates, as well as all other templates.

## Features

### ✅ Implemented Features

1. **Database Schema**: SQLite database with tables for campaigns, recipients, and click events
2. **Click Tracking URLs**: Automatic replacement of email URLs with tracking URLs
3. **Analytics API**: Comprehensive endpoints for campaign performance analysis
4. **Multi-Template Support**: Works with all email templates (HORIZON1-6, ENTRA1-6)
5. **Detailed Button Tracking**: Tracks different types of CTA buttons separately

## Database Schema

### Tables

1. **campaigns**
   - `id`: Unique campaign identifier
   - `name`: Campaign name
   - `subject`: Email subject
   - `template_type`: Template used (horizon6, entra6, etc.)
   - `created_at`: Campaign creation timestamp
   - `total_recipients`: Number of recipients
   - `total_sent`: Number of successfully sent emails

2. **email_recipients**
   - `id`: Unique recipient identifier
   - `campaign_id`: Reference to campaign
   - `email_address`: Recipient email
   - `sent_at`: Email sent timestamp

3. **click_events**
   - `id`: Unique click event identifier
   - `recipient_id`: Reference to recipient
   - `campaign_id`: Reference to campaign
   - `button_type`: Type of button clicked
   - `original_url`: Original destination URL
   - `clicked_at`: Click timestamp
   - `ip_address`: User's IP address
   - `user_agent`: User's browser information

## Tracked Button Types

The system automatically identifies and tracks different types of CTA buttons:

- **LOGIN_NOW**: Login buttons
- **CLAIM_70_OFF**: 70% discount buttons
- **CLAIM_60_OFF**: 60% discount buttons (ENTRA templates)
- **CLAIM_20_OFF**: 20% discount buttons
- **WEBSITE_LINK**: General website links
- **CTA_BUTTON**: Generic call-to-action buttons

## API Endpoints

### Email Sending (Enhanced)
```
POST /send-email
```
Now automatically creates campaigns and enables click tracking for HTML emails.

**Response includes:**
- `campaign_id`: Unique campaign identifier
- `tracking_enabled`: Boolean indicating if tracking is active
- `analytics_url`: Direct link to campaign analytics

### Click Tracking
```
GET /track-click/{tracking_id}
```
Handles click events and redirects users to original URLs while recording analytics.

### Analytics Endpoints

#### Campaign Analytics
```
GET /analytics/campaign/{campaign_id}
```
Returns detailed analytics for a specific campaign:
- Total recipients and clicks
- Unique clickers
- Click rate percentage
- Click statistics by button type

#### All Campaigns
```
GET /analytics/campaigns
```
Returns list of all campaigns with basic statistics.

#### Analytics Summary
```
GET /analytics/summary
```
Returns overall system analytics:
- Total campaigns and emails sent
- Overall click rates
- Template performance comparison

## HORIZON6 Template CTA Buttons

The HORIZON6 template contains these trackable CTA buttons:

1. **LOGIN NOW** → `https://www.fundedhorizon.com/login`
2. **CLAIM 70% OFF** → `https://www.fundedhorizon.com`
3. **CLAIM 20% OFF** → `https://www.fundedhorizon.com`
4. **Website Link** → `https://www.fundedhorizon.com`

## ENTRA6 Template CTA Buttons

The ENTRA6 template contains these trackable CTA buttons:

1. **LOGIN NOW** → `https://www.fxentra.com/login`
2. **CLAIM 60% OFF** → `https://www.fxentra.com`
3. **CLAIM 20% OFF** → `https://www.fxentra.com`
4. **Website Link** → `https://www.fxentra.com`

## How It Works

1. **Email Sending**: When an HTML email is sent, the system:
   - Creates a campaign record
   - Generates unique recipient IDs
   - Replaces all CTA URLs with tracking URLs
   - Stores tracking information

2. **Click Tracking**: When a user clicks a tracked link:
   - System records the click event with timestamp, IP, and user agent
   - User is redirected to the original URL
   - Analytics are updated in real-time

3. **Analytics**: Access comprehensive reports through API endpoints

## Usage Examples

### Send Tracked Email
```python
import requests

email_data = {
    "to_emails": "<EMAIL>",
    "subject": "Special Offer - 70% Off!",
    "email_type": "html",
    "template_type": "horizon6"
}

response = requests.post("http://localhost:8000/send-email", json=email_data)
result = response.json()
campaign_id = result["campaign_id"]
```

### Get Campaign Analytics
```python
response = requests.get(f"http://localhost:8000/analytics/campaign/{campaign_id}")
analytics = response.json()

print(f"Click Rate: {analytics['click_rate']}%")
print(f"Total Clicks: {analytics['total_clicks']}")
```

## Testing

Run the test script to verify functionality:

```bash
python test_tracking.py
```

This will:
- Send test emails with both templates
- Demonstrate analytics endpoints
- Show tracking URLs in action

## Benefits

1. **User Engagement Insights**: See which CTA buttons perform best
2. **Campaign Performance**: Track success rates across different templates
3. **A/B Testing**: Compare performance between HORIZON6 and ENTRA6
4. **ROI Measurement**: Understand email marketing effectiveness
5. **User Behavior**: Analyze click patterns and timing

## Security & Privacy

- Click tracking uses hashed IDs for privacy
- IP addresses and user agents are stored for analytics
- All tracking data is stored locally in SQLite database
- No external tracking services are used

## Next Steps

The system is ready for production use. Consider adding:
- Click heatmaps
- Geographic analytics
- Time-based performance reports
- Email client analytics
- Unsubscribe tracking
