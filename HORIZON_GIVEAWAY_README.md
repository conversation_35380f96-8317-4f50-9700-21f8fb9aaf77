# HORIZON_GIVEAWAY Template

## Overview

The **HORIZON_GIVEAWAY** template is a specially designed email template for running exciting giveaway campaigns. It features an eye-catching design with vibrant colors, clear prize structures, and multiple call-to-action buttons to maximize engagement.

## Template Features

### 🎨 **Visual Design**
- **Gradient Background**: Beautiful orange-to-gold gradient background
- **Professional Layout**: Clean, modern design with rounded corners and shadows
- **Emoji Integration**: Strategic use of emojis to create excitement
- **Responsive Design**: Works perfectly on desktop and mobile devices

### 🏆 **Giveaway Elements**
- **Grand Prize Display**: Prominent showcase of main prizes
- **Prize Hierarchy**: Clear 1st, 2nd, and 3rd place prize structure
- **Entry Instructions**: Step-by-step guide for participants
- **Urgency Messaging**: "Ends Soon" messaging to create urgency

### 📊 **Click Tracking**
All CTA buttons are automatically tracked with the following button types:
- `ENTER_GIVEAWAY` - Main giveaway entry button
- `LOGIN_NOW` - Login to enter button
- `LEARN_MORE` - Learn more about the giveaway
- `WEBSITE_LINK` - Visit website button
- `SOCIAL_MEDIA` - Social media links

## Template Structure

### Header Section
- **Logo**: Funded Horizon logo prominently displayed
- **Main Title**: "🎉 EXCLUSIVE GIVEAWAY! 🎉"
- **Subtitle**: "WIN BIG WITH FUNDED HORIZON!"

### Prize Section
- **Grand Prize**: $10,000 Funded Account 🥇
- **Second Prize**: $5,000 Funded Account 🥈
- **Third Prize**: $2,500 Funded Account 🥉
- **Primary CTA**: "🎯 ENTER GIVEAWAY NOW!"

### Instructions Section
- **Step 1**: Create your Funded Horizon account
- **Step 2**: Follow us on social media
- **Step 3**: Share this giveaway with friends
- **Step 4**: Complete your entry form

### Action Buttons
- **🔐 LOGIN TO ENTER** - Direct login link
- **📖 LEARN MORE** - Additional information
- **⏰ Urgency Banner** - "GIVEAWAY ENDS SOON!"

### Social Media Section
- Facebook, Twitter, Instagram, Telegram links
- Branded social media icons

### Footer
- **Website Link**: Direct link to FundedHorizon.com
- **Unsubscribe Link**: Automatic unsubscribe functionality

## Usage Examples

### Basic Giveaway Email
```python
import requests

email_data = {
    "to_emails": "<EMAIL>",
    "subject": "🎉 EXCLUSIVE GIVEAWAY - Win Up to $10,000!",
    "email_type": "html",
    "template_type": "horizon_giveaway"
}

response = requests.post("http://localhost:8000/send-email", json=email_data)
```

### Bulk Giveaway Campaign
```python
email_data = {
    "to_emails": "<EMAIL>,<EMAIL>,<EMAIL>",
    "subject": "🏆 Last Chance - $10K Giveaway Ends Soon!",
    "email_type": "html",
    "template_type": "horizon_giveaway"
}

response = requests.post("http://localhost:8000/send-email", json=email_data)
campaign_id = response.json().get('campaign_id')
```

### Check Giveaway Analytics
```python
# Get campaign performance
response = requests.get(f"http://localhost:8000/analytics/campaign/{campaign_id}")
analytics = response.json()

print(f"Giveaway Entries (clicks): {analytics['total_clicks']}")
print(f"Participation Rate: {analytics['click_rate']}%")
```

## Customization Options

### Subject Line Suggestions
- "🎉 EXCLUSIVE GIVEAWAY - Win Up to $10,000 Funded Account!"
- "🏆 Last Chance - Enter Our $10K Giveaway!"
- "🎁 You're Invited - Exclusive Funded Account Giveaway"
- "⏰ Ending Soon - Don't Miss Your Chance to Win!"
- "🚀 MEGA GIVEAWAY - $10,000 Up for Grabs!"

### Best Practices
1. **Timing**: Send during peak engagement hours (10 AM - 2 PM)
2. **Frequency**: Don't oversend - space giveaway emails appropriately
3. **Segmentation**: Target engaged subscribers for better results
4. **Follow-up**: Send reminder emails as deadline approaches
5. **Analytics**: Monitor click rates and adjust strategy accordingly

## Analytics Tracking

The template tracks the following metrics:
- **Total Recipients**: Number of emails sent
- **Open Rate**: Email open statistics
- **Click Rate**: Percentage of recipients who clicked CTAs
- **Button Performance**: Which CTAs perform best
- **Entry Conversion**: How many clicked "Enter Giveaway"

### Key Metrics to Monitor
- **Entry Rate**: Clicks on "ENTER GIVEAWAY NOW!" button
- **Login Rate**: Clicks on "LOGIN TO ENTER" button
- **Engagement Rate**: Overall click activity
- **Social Sharing**: Social media link clicks

## Testing

Use the test script to verify the template:
```bash
python test_giveaway_template.py
```

This will:
- Verify template loads correctly
- Send a test giveaway email
- Check analytics functionality
- Display all template features

## Integration with Campaigns

### Giveaway Campaign Flow
1. **Announcement**: Send initial giveaway email
2. **Reminders**: Send follow-up emails with urgency
3. **Last Chance**: Final reminder before deadline
4. **Winner Announcement**: Results email (use different template)

### Cross-Platform Integration
- **Website**: Link to dedicated giveaway landing page
- **Social Media**: Coordinate with social media campaigns
- **Email Sequences**: Part of larger email marketing funnel

## Performance Optimization

### Expected Performance
- **Open Rate**: 25-35% (higher due to exciting subject)
- **Click Rate**: 8-15% (giveaways typically perform well)
- **Entry Rate**: 5-10% (actual giveaway participation)

### Optimization Tips
1. **A/B Test**: Try different subject lines
2. **Timing**: Test different send times
3. **Prizes**: Adjust prize values based on audience
4. **Urgency**: Experiment with deadline messaging
5. **Social Proof**: Add testimonials or winner stories

## Compliance & Legal

### Important Considerations
- **Terms & Conditions**: Link to official giveaway rules
- **Legal Compliance**: Follow local giveaway regulations
- **Age Restrictions**: Include age requirements if applicable
- **Geographic Limits**: Specify eligible regions
- **Winner Selection**: Clearly state selection process

The HORIZON_GIVEAWAY template is designed to maximize engagement and participation while maintaining professional branding and full analytics tracking.
