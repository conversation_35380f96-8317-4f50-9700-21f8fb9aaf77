#!/usr/bin/env python3
"""
Test script to demonstrate email click tracking functionality
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"

def test_email_sending_with_tracking():
    """Test sending an email with click tracking enabled"""
    print("🚀 Testing Email Click Tracking System")
    print("=" * 50)
    
    # Test 1: Send HTML email with HORIZON6 template
    print("\n1. Sending HTML email with HORIZON6 template...")
    
    email_data = {
        "to_emails": TEST_EMAIL,
        "subject": "Test Email - HORIZON6 Click Tracking",
        "message": "",
        "email_type": "html",
        "template_type": "horizon6"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/send-email", json=email_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Email sent successfully!")
            print(f"   Campaign ID: {result.get('campaign_id', 'N/A')}")
            print(f"   Tracking enabled: {result.get('tracking_enabled', False)}")
            print(f"   Analytics URL: {result.get('analytics_url', 'N/A')}")
            
            campaign_id = result.get('campaign_id')
            if campaign_id:
                return campaign_id
        else:
            print(f"❌ Failed to send email: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Error sending email: {e}")
    
    return None

def test_analytics_endpoints(campaign_id):
    """Test analytics endpoints"""
    print(f"\n2. Testing analytics endpoints for campaign: {campaign_id}")
    
    # Test campaign analytics
    try:
        response = requests.get(f"{BASE_URL}/analytics/campaign/{campaign_id}")
        if response.status_code == 200:
            analytics = response.json()
            print("✅ Campaign Analytics:")
            print(f"   Campaign Name: {analytics.get('campaign_name')}")
            print(f"   Total Recipients: {analytics.get('total_recipients')}")
            print(f"   Total Clicks: {analytics.get('total_clicks')}")
            print(f"   Unique Clickers: {analytics.get('unique_clickers')}")
            print(f"   Click Rate: {analytics.get('click_rate')}%")
            print(f"   Click Stats: {analytics.get('click_stats_by_button')}")
        else:
            print(f"❌ Failed to get campaign analytics: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting campaign analytics: {e}")
    
    # Test overall analytics summary
    try:
        response = requests.get(f"{BASE_URL}/analytics/summary")
        if response.status_code == 200:
            summary = response.json()
            print("\n✅ Overall Analytics Summary:")
            print(f"   Total Campaigns: {summary.get('total_campaigns')}")
            print(f"   Total Emails Sent: {summary.get('total_emails_sent')}")
            print(f"   Total Clicks: {summary.get('total_clicks')}")
            print(f"   Overall Click Rate: {summary.get('overall_click_rate')}%")
        else:
            print(f"❌ Failed to get analytics summary: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting analytics summary: {e}")
    
    # Test campaigns list
    try:
        response = requests.get(f"{BASE_URL}/analytics/campaigns")
        if response.status_code == 200:
            campaigns = response.json()
            print(f"\n✅ Found {len(campaigns.get('campaigns', []))} campaigns")
            for campaign in campaigns.get('campaigns', [])[:3]:  # Show first 3
                print(f"   - {campaign.get('name')} (Click Rate: {campaign.get('click_rate')}%)")
        else:
            print(f"❌ Failed to get campaigns list: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting campaigns list: {e}")

def test_entra6_template():
    """Test ENTRA6 template with tracking"""
    print("\n3. Testing ENTRA6 template with click tracking...")
    
    email_data = {
        "to_emails": TEST_EMAIL,
        "subject": "Test Email - ENTRA6 Click Tracking",
        "message": "",
        "email_type": "html",
        "template_type": "entra6"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/send-email", json=email_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ ENTRA6 email sent successfully!")
            print(f"   Campaign ID: {result.get('campaign_id', 'N/A')}")
            return result.get('campaign_id')
        else:
            print(f"❌ Failed to send ENTRA6 email: {response.status_code}")
    except Exception as e:
        print(f"❌ Error sending ENTRA6 email: {e}")
    
    return None

def main():
    """Main test function"""
    print("Starting Email Click Tracking Tests...")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    
    # Test server connection
    try:
        response = requests.get(f"{BASE_URL}/test-smtp-connection")
        if response.status_code != 200:
            print("⚠️  SMTP connection test failed, but continuing with tracking tests...")
    except Exception as e:
        print(f"⚠️  Could not connect to server: {e}")
        print("Make sure to run: python main.py")
        return
    
    # Test HORIZON6 template
    campaign_id1 = test_email_sending_with_tracking()
    
    if campaign_id1:
        test_analytics_endpoints(campaign_id1)
    
    # Test ENTRA6 template
    campaign_id2 = test_entra6_template()
    
    print("\n" + "=" * 50)
    print("🎉 Click Tracking Test Complete!")
    print("\nTo test actual click tracking:")
    print("1. Check your email inbox")
    print("2. Click on the CTA buttons in the emails")
    print("3. Check analytics endpoints to see click data")
    print(f"4. Visit: {BASE_URL}/analytics/summary")
    
    if campaign_id1:
        print(f"5. Campaign 1 analytics: {BASE_URL}/analytics/campaign/{campaign_id1}")
    if campaign_id2:
        print(f"6. Campaign 2 analytics: {BASE_URL}/analytics/campaign/{campaign_id2}")

if __name__ == "__main__":
    main()
