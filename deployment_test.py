#!/usr/bin/env python3
"""
Deployment test script to verify the application works correctly
"""

import os
import sys

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn imported successfully")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        import sqlite3
        print("✅ SQLite3 imported successfully")
    except ImportError as e:
        print(f"❌ SQLite3 import failed: {e}")
        return False
    
    try:
        import smtplib
        print("✅ SMTP library imported successfully")
    except ImportError as e:
        print(f"❌ SMTP library import failed: {e}")
        return False
    
    return True

def test_main_module():
    """Test that the main module can be imported without errors"""
    print("\n🔍 Testing main module...")
    
    try:
        import main
        print("✅ Main module imported successfully")
        
        # Test that the app object exists
        if hasattr(main, 'app'):
            print("✅ FastAPI app object found")
        else:
            print("❌ FastAPI app object not found")
            return False
            
        # Test database initialization
        if os.path.exists('email_tracking.db'):
            print("✅ Database file created")
        else:
            print("⚠️  Database file not found (will be created on first run)")
        
        return True
        
    except Exception as e:
        print(f"❌ Main module import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_port_configuration():
    """Test port configuration for Heroku"""
    print("\n🔍 Testing port configuration...")
    
    # Test with PORT environment variable
    os.environ['PORT'] = '5000'
    
    try:
        import main
        print("✅ Port configuration works with environment variable")
        return True
    except Exception as e:
        print(f"❌ Port configuration failed: {e}")
        return False

def test_hello_module():
    """Test that hello.py doesn't have syntax errors"""
    print("\n🔍 Testing hello.py module...")
    
    try:
        import hello
        print("✅ Hello module imported successfully")
        return True
    except Exception as e:
        print(f"❌ Hello module import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Deployment Test Suite")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Run tests
    tests = [
        test_imports,
        test_main_module,
        test_port_configuration,
        test_hello_module
    ]
    
    for test in tests:
        if not test():
            all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All tests passed! Ready for deployment.")
        print("\nNext steps:")
        print("1. git add .")
        print("2. git commit -m 'Fix deployment issues'")
        print("3. git push heroku main")
    else:
        print("❌ Some tests failed. Please fix the issues before deploying.")
        sys.exit(1)

if __name__ == "__main__":
    main()
