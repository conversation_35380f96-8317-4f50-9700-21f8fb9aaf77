from fastapi import Fast<PERSON><PERSON>, HTTPException
from pydantic import BaseModel, EmailStr, validator
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email_validator import validate_email, EmailNotValidError
import time
from typing import List
import asyncio
from enum import Enum
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Email Sender API")

# SMTP Configuration
SMTP_SERVER = "smtp.hostinger.com"
SMTP_PORT = 587
SMTP_USERNAME = "<EMAIL>"  # Replace with your Hostinger email
SMTP_PASSWORD = "Fxentra@123"  # Replace with your Hostinger email password

# Email sending configuration
BATCH_SIZE = 1000  # Number of emails to send in each batch
DELAY_BETWEEN_BATCHES = 5  # Delay in seconds between batches to prevent rate limiting

class EmailType(str, Enum):
    PLAIN_TEXT = "plain_text"
    HTML = "html"

class TemplateType(str, Enum):
    HORIZON = "horizon"
    ENTRA = "entra"

def get_html_template(template_type: TemplateType = TemplateType.HORIZON) -> str:
    """Load the HTML email template based on template type"""
    try:
        if template_type == TemplateType.HORIZON:
            clean_html = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Funded Horizon - Massive Discounts</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #181a23;">
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #181a23; background-image: linear-gradient(rgba(30,58,138,0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(255,152,0,0.05) 1px, transparent 1px); background-size: 50px 50px;">
        <tr>
            <td align="center" style="padding: 30px 20px;">
                <!-- Main Container -->
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: transparent;">
                    <!-- Header Row -->
                    <tr>
                        <td style="padding-bottom: 50px;">
                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td align="left" style="vertical-align: top;">
                                        <!-- Login Now Button -->
                                        <a href="https://www.fundedhorizon.com/login" style="display: inline-block; background-color: rgba(30,58,138,0.15); border: 2px solid #1e3a8a; color: #ff9800; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 700; text-decoration: none; text-transform: uppercase; letter-spacing: 1px;">
                                            LOGIN NOW
                                        </a>
                                    </td>
                                    <td align="right" style="vertical-align: top;">
                                        <!-- Logo Image -->
                                        <img src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751314996/logo_sa7mt5.png" alt="Funded Horizon Logo" style="width: 100px; height: auto; max-width: 100px; display: block;" width="100" />
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Main Title -->
                    <tr>
                        <td align="center" style="padding-bottom: 50px;">
                            <h1 style="margin: 0; font-size: 42px; font-weight: 900; color: #ff9800; text-transform: uppercase; letter-spacing: 2px; line-height: 1.1; text-align: center;">
                                HUGE DISCOUNTS<br>
                                LIMITED TIME ONLY!
                            </h1>
                        </td>
                    </tr>
                    
                    <!-- Content Section -->
                    <tr>
                        <td style="padding-bottom: 50px;">
                            <!-- Premium Offer Box - Full Width -->
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: rgba(30,58,138,0.10); border: 3px solid #1e3a8a; border-radius: 20px; margin-bottom: 30px;">
                                <tr>
                                    <td style="padding: 30px; text-align: center;">
                                        <h2 style="margin: 0 0 15px 0; font-size: 28px; font-weight: 900; color: #ff9800; text-transform: uppercase; line-height: 1.2;">
                                            LIMITED TIME: 70% OFF ON PREMIUM PACKAGES!
                                        </h2>
                                        <p style="margin: 0 0 25px 0; font-size: 14px; color: #1e3a8a; text-transform: uppercase; letter-spacing: 1px; font-weight: 600;">
                                            APPLICABLE ON: 50K, 100K, 200K, AND 500K ACCOUNTS
                                        </p>
                                        <a href="https://www.fundedhorizon.com" style="display: inline-block; background-color: rgba(255,152,0,0.15); border: 3px solid #ff9800; color: #1e3a8a; padding: 12px 30px; border-radius: 10px; font-size: 16px; font-weight: 900; text-decoration: none; text-transform: uppercase; letter-spacing: 1px;">
                                            CLAIM 70% OFF
                                        </a>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Bottom Section with Pricing and Sitewide Offer -->
                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <!-- Left: Pricing Table -->
                                    <td width="280" style="vertical-align: top; padding-right: 20px;">
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <!-- Accounts Column -->
                                                <td width="48%" style="padding-right: 10px;">
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                        <tr>
                                                            <td style="background-color: rgba(255,152,0,0.15); border: 3px solid #ff9800; padding: 12px; text-align: center; font-weight: 900; color: #1e3a8a; text-transform: uppercase; border-radius: 10px; font-size: 14px; margin-bottom: 12px;">
                                                                ACCOUNTS
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px; margin-bottom: 12px;">
                                                                1K
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px; margin-bottom: 12px;">
                                                                50K
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px; margin-bottom: 12px;">
                                                                100K
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px;">
                                                                200K
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                
                                                <!-- Price Column -->
                                                <td width="48%" style="padding-left: 10px;">
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                        <tr>
                                                            <td style="background-color: rgba(255,152,0,0.15); border: 3px solid #ff9800; padding: 12px; text-align: center; font-weight: 900; color: #1e3a8a; text-transform: uppercase; border-radius: 10px; font-size: 14px; margin-bottom: 12px;">
                                                                PRICE
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px; margin-bottom: 12px;">
                                                                $8
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px; margin-bottom: 12px;">
                                                                $45
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px; margin-bottom: 12px;">
                                                                $72
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(30,58,138,0.10); border: 3px solid rgba(30,58,138,0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: #ff9800; border-radius: 10px;">
                                                                $115
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    
                                    <!-- Right: Sitewide Offer Box -->
                                    <td width="300" style="vertical-align: top; padding-left: 20px;">
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: rgba(255,152,0,0.10); border: 3px solid #ff9800; border-radius: 20px; height: 100%;">
                                            <tr>
                                                <td style="padding: 40px 30px; text-align: center; vertical-align: middle;">
                                                    <h2 style="margin: 0 0 20px 0; font-size: 26px; font-weight: 900; color: #1e3a8a; text-transform: uppercase; line-height: 1.2;">
                                                        GET 20% OFF SITEWIDE
                                                    </h2>
                                                    <p style="margin: 0 0 25px 0; font-size: 14px; color: #ff9800; text-transform: uppercase; letter-spacing: 1px; font-weight: 600;">
                                                        VALID ON: 1K, 3K, 5K, 10K, AND 25K ACCOUNTS
                                                    </p>
                                                    <a href="https://www.fundedhorizon.com" style="display: inline-block; background-color: rgba(30,58,138,0.15); border: 3px solid #1e3a8a; color: #ff9800; padding: 12px 25px; border-radius: 10px; font-size: 16px; font-weight: 900; text-decoration: none; text-transform: uppercase; letter-spacing: 1px;">
                                                        CLAIM 20% OFF
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Bottom Text -->
                    <tr>
                        <td align="center" style="padding-bottom: 30px;">
                            <p style="margin: 0; font-size: 22px; font-weight: 900; color: #ff9800; text-transform: uppercase; letter-spacing: 1px; text-align: center;">
                                HURRY! THESE OFFERS ARE VALID FOR A LIMITED TIME ONLY.
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Website URL -->
                    <tr>
                        <td align="center">
                            <a href="https://www.fundedhorizon.com" style="display: inline-block; background-color: rgba(30,58,138,0.15); border: 3px solid #1e3a8a; color: #ff9800; padding: 15px 40px; border-radius: 25px; font-size: 18px; font-weight: 700; text-decoration: none;">
                                www.fundedhorizon.com
                            </a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>'''
        elif template_type == TemplateType.ENTRA:
            clean_html = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FXentra - Massive Discounts</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #123530;">
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #123530; background-image: linear-gradient(rgba(0, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 255, 255, 0.05) 1px, transparent 1px); background-size: 50px 50px;">
        <tr>
            <td a<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Funded Horizon Discounts</title>
    <style>
        @keyframes gradientBG {{
            0% {{ background-position: 0% 50%; }}
            50% {{ background-position: 100% 50%; }}
            100lign="center" style="padding: 30px 20px;">
                <!-- Main Container -->
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: transparent;">
                    <!-- Header Row -->
                    <tr>
                        <td style="padding-bottom: 50px;">
                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td align="left" style="vertical-align: top;">
                                        <!-- Login Now Button -->
                                        <a href="https://www.fxentra.com/login" style="display: inline-block; background-color: rgba(0, 255, 255, 0.2); border: 2px solid #00ffff; color: white; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 700; text-decoration: none; text-transform: uppercase; letter-spacing: 1px;">
                                            LOGIN NOW
                                        </a>
                                    </td>
                                    <td align="right" style="vertical-align: top;">
                                        <!-- Logo Image -->
                                        <img src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751307308/fxentra-logo_kpk4td.png" alt="FXentra Logo" style="width: 100px; height: auto; max-width: 100px; display: block;" width="100" />
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Main Title -->
                    <tr>
                        <td align="center" style="padding-bottom: 50px;">
                            <h1 style="margin: 0; font-size: 42px; font-weight: 900; color: white; text-transform: uppercase; letter-spacing: 2px; line-height: 1.1; text-align: center;">
                                MASSIVE DISCOUNTS<br>
                                LIMITED TIME ONLY!
                            </h1>
                        </td>
                    </tr>
                    
                    <!-- Content Section -->
                    <tr>
                        <td style="padding-bottom: 50px;">
                            <!-- Premium Offer Box - Full Width -->
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid #00ffff; border-radius: 20px; margin-bottom: 30px;">
                                <tr>
                                    <td style="padding: 30px; text-align: center;">
                                        <h2 style="margin: 0 0 15px 0; font-size: 28px; font-weight: 900; color: white; text-transform: uppercase; line-height: 1.2;">
                                            GET 70% OFF ON PREMIUM<br>PACKAGES
                                        </h2>
                                        <p style="margin: 0 0 25px 0; font-size: 14px; color: rgba(255, 255, 255, 0.8); text-transform: uppercase; letter-spacing: 1px; font-weight: 600;">
                                            APPLIES TO: 50K, 100K, AND 200K ACCOUNTS
                                        </p>
                                        <a href="https://www.fxentra.com/" style="display: inline-block; background-color: rgba(0, 255, 255, 0.2); border: 3px solid #00ffff; color: white; padding: 12px 30px; border-radius: 10px; font-size: 16px; font-weight: 900; text-decoration: none; text-transform: uppercase; letter-spacing: 1px;">
                                            CLAIM 60% OFF
                                        </a>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Bottom Section with Pricing and Sitewide Offer -->
                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <!-- Left: Pricing Table -->
                                    <td width="280" style="vertical-align: top; padding-right: 20px;">
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <!-- Accounts Column -->
                                                <td width="48%" style="padding-right: 10px;">
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.2); border: 3px solid #00ffff; padding: 12px; text-align: center; font-weight: 900; color: white; text-transform: uppercase; border-radius: 10px; font-size: 14px; margin-bottom: 12px;">
                                                                ACCOUNTS
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px; margin-bottom: 12px;">
                                                                1K
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px; margin-bottom: 12px;">
                                                                50K
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px; margin-bottom: 12px;">
                                                                100K
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px;">
                                                                200K
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                
                                                <!-- Price Column -->
                                                <td width="48%" style="padding-left: 10px;">
                                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.2); border: 3px solid #00ffff; padding: 12px; text-align: center; font-weight: 900; color: white; text-transform: uppercase; border-radius: 10px; font-size: 14px; margin-bottom: 12px;">
                                                                PRICE
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px; margin-bottom: 12px;">
                                                                $6
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px; margin-bottom: 12px;">
                                                                $50
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px; margin-bottom: 12px;">
                                                                $75
                                                            </td>
                                                        </tr>
                                                        <tr><td style="height: 12px;"></td></tr>
                                                        <tr>
                                                            <td style="background-color: rgba(0, 255, 255, 0.1); border: 3px solid rgba(0, 255, 255, 0.4); padding: 15px; text-align: center; font-size: 24px; font-weight: 900; color: white; border-radius: 10px;">
                                                                $125
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    
                                    <!-- Right: Sitewide Offer Box -->
                                    <td width="300" style="vertical-align: top; padding-left: 20px;">
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: rgba(0, 255, 255, 0.15); border: 3px solid #00ffff; border-radius: 20px; height: 100%;">
                                            <tr>
                                                <td style="padding: 40px 30px; text-align: center; vertical-align: middle;">
                                                    <h2 style="margin: 0 0 20px 0; font-size: 26px; font-weight: 900; color: white; text-transform: uppercase; line-height: 1.2;">
                                                        ENJOY 20% OFF SITEWIDE
                                                    </h2>
                                                    <p style="margin: 0 0 25px 0; font-size: 14px; color: rgba(255, 255, 255, 0.8); text-transform: uppercase; letter-spacing: 1px; font-weight: 600;">
                                                        VALID ON: 1K, 3K, 5K, 10K, AND 25K ACCOUNTS
                                                    </p>
                                                    <a href="https://www.fxentra.com/" style="display: inline-block; background-color: rgba(0, 255, 255, 0.2); border: 3px solid #00ffff; color: white; padding: 12px 25px; border-radius: 10px; font-size: 16px; font-weight: 900; text-decoration: none; text-transform: uppercase; letter-spacing: 1px;">
                                                        CLAIM 20% OFF
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Bottom Text -->
                    <tr>
                        <td align="center" style="padding-bottom: 30px;">
                            <p style="margin: 0; font-size: 22px; font-weight: 900; color: white; text-transform: uppercase; letter-spacing: 1px; text-align: center;">
                                OFFERS VALID FOR A LIMITED TIME ONLY.
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Website URL -->
                    <tr>
                        <td align="center">
                            <a href="https://www.fxentra.com" style="display: inline-block; background-color: rgba(0, 255, 255, 0.2); border: 3px solid #00ffff; color: white; padding: 15px 40px; border-radius: 25px; font-size: 18px; font-weight: 700; text-decoration: none;">
                                www.fxentra.com
                            </a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>'''
        else:
            raise ValueError(f"Invalid template type: {template_type}")
        
        logger.info(f"HTML template '{template_type}' loaded and cleaned successfully")
        return clean_html
    except Exception as e:
        logger.error(f"Failed to load HTML template: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to load HTML template: {str(e)}"
        )

class EmailRequest(BaseModel):
    to_emails: str  # Comma-separated email addresses
    subject: str
    message: str = ""  # Optional for HTML emails
    email_type: EmailType = EmailType.PLAIN_TEXT  # Default to plain text
    template_type: TemplateType = TemplateType.HORIZON  # Default to horizon template

    @validator('to_emails')
    def validate_emails(cls, v):
        # Split by comma and strip whitespace
        emails = [email.strip() for email in v.split(',')]
        # Validate each email
        for email in emails:
            try:
                validate_email(email)
            except EmailNotValidError as e:
                raise ValueError(f"Invalid email address: {email}")
        return v

def send_email_batch(recipients: List[str], subject: str, message: str, email_type: EmailType, template_type: TemplateType = TemplateType.HORIZON) -> dict:
    """Send email to a batch of recipients"""
    successful_sends = []
    failed_sends = []
    server = None
    
    try:
        # Create SMTP session with better error handling
        logger.info(f"Connecting to SMTP server: {SMTP_SERVER}:{SMTP_PORT}")
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT, timeout=30)
        server.set_debuglevel(0)  # Set to 1 for debugging if needed
        
        # Start TLS encryption
        server.starttls()
        
        # Login to SMTP server
        server.login(SMTP_USERNAME, SMTP_PASSWORD)
        logger.info("SMTP connection established successfully")
        
        # Send individual emails to each recipient
        for recipient in recipients:
            try:
                # Create message for each recipient
                msg = MIMEMultipart('alternative')
                msg['From'] = SMTP_USERNAME
                msg['To'] = recipient
                msg['Subject'] = subject

                # Attach content based on email type
                if email_type == EmailType.HTML:
                    logger.info(f"Preparing HTML email for {recipient}")
                    # Use the HTML template based on template type
                    html_content = get_html_template(template_type)
                    
                    # Verify HTML content before sending
                    if not html_content or len(html_content) < 100:  # Basic validation
                        raise ValueError("HTML template content is invalid or empty")
                        
                    # Create plain text version
                    plain_text = "Please view this email in an HTML-compatible email client to see our special offers!"
                    msg.attach(MIMEText(plain_text, 'plain'))
                    msg.attach(MIMEText(html_content, 'html'))
                    logger.info(f"HTML template attached to email for {recipient}")
                else:
                    logger.info(f"Preparing plain text email for {recipient}")
                    msg.attach(MIMEText(message, 'plain'))

                # Send email to individual recipient
                server.send_message(msg)
                successful_sends.append(recipient)
                logger.info(f"Email sent successfully to {recipient}")
                
                # Add a small delay between sends to prevent rate limiting
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Failed to send email to {recipient}: {str(e)}")
                failed_sends.append(recipient)
                continue
        
        logger.info(f"Batch completed. Successful: {len(successful_sends)}, Failed: {len(failed_sends)}")
        
        return {
            "success": len(failed_sends) == 0,
            "recipients": recipients,
            "successful_sends": successful_sends,
            "failed_sends": failed_sends
        }
    except smtplib.SMTPAuthenticationError as e:
        logger.error(f"SMTP authentication failed: {str(e)}")
        return {
            "success": False,
            "recipients": recipients,
            "error": f"SMTP authentication failed: {str(e)}"
        }
    except smtplib.SMTPConnectError as e:
        logger.error(f"SMTP connection failed: {str(e)}")
        return {
            "success": False,
            "recipients": recipients,
            "error": f"SMTP connection failed: {str(e)}"
        }
    except smtplib.SMTPException as e:
        logger.error(f"SMTP error: {str(e)}")
        return {
            "success": False,
            "recipients": recipients,
            "error": f"SMTP error: {str(e)}"
        }
    except Exception as e:
        logger.error(f"Failed to process email batch: {str(e)}")
        return {
            "success": False,
            "recipients": recipients,
            "error": str(e)
        }
    finally:
        # Always close the SMTP connection
        if server:
            try:
                server.quit()
                logger.info("SMTP connection closed")
            except Exception as e:
                logger.error(f"Error closing SMTP connection: {str(e)}")

@app.get("/verify-html-template")
async def verify_html_template(template_type: TemplateType = TemplateType.HORIZON):
    """Verify that the HTML template can be loaded correctly"""
    try:
        html_content = get_html_template(template_type)
        return {
            "status": "success",
            "message": f"HTML template '{template_type}' loaded successfully",
            "template_size": len(html_content),
            "template_preview": html_content[:200] + "..."  # Show first 200 chars
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/test-smtp-connection")
async def test_smtp_connection():
    """Test SMTP connection to verify credentials and server settings"""
    try:
        logger.info("Testing SMTP connection...")
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT, timeout=30)
        server.set_debuglevel(0)
        
        # Start TLS encryption
        server.starttls()
        
        # Login to SMTP server
        server.login(SMTP_USERNAME, SMTP_PASSWORD)
        
        # Close connection
        server.quit()
        
        return {
            "status": "success",
            "message": "SMTP connection test successful",
            "server": SMTP_SERVER,
            "port": SMTP_PORT,
            "username": SMTP_USERNAME
        }
    except smtplib.SMTPAuthenticationError as e:
        logger.error(f"SMTP authentication failed: {str(e)}")
        raise HTTPException(status_code=401, detail=f"SMTP authentication failed: {str(e)}")
    except smtplib.SMTPConnectError as e:
        logger.error(f"SMTP connection failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SMTP connection failed: {str(e)}")
    except smtplib.SMTPException as e:
        logger.error(f"SMTP error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SMTP error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

@app.post("/send-email")
async def send_email(email_request: EmailRequest):
    try:
        logger.info(f"Received email request - Type: {email_request.email_type}, Template: {email_request.template_type}")
        
        # Split emails and create a list
        recipient_list = [email.strip() for email in email_request.to_emails.split(',')]
        total_recipients = len(recipient_list)
        
        if total_recipients > 500:
            raise HTTPException(
                status_code=400,
                detail="Maximum number of recipients (500) exceeded"
            )

        # Process emails in batches
        results = []
        for i in range(0, total_recipients, BATCH_SIZE):
            batch = recipient_list[i:i + BATCH_SIZE]
            logger.info(f"Processing batch {i//BATCH_SIZE + 1} with {len(batch)} recipients")
            
            batch_result = send_email_batch(
                batch,
                email_request.subject,
                email_request.message,
                email_request.email_type,
                email_request.template_type
            )
            results.append(batch_result)
            
            # Add delay between batches if not the last batch
            if i + BATCH_SIZE < total_recipients:
                await asyncio.sleep(DELAY_BETWEEN_BATCHES)

        # Process results
        successful_sends = sum(1 for r in results if r["success"])
        failed_sends = total_recipients - successful_sends
        
        logger.info(f"Email sending completed - Success: {successful_sends}, Failed: {failed_sends}")
        
        return {
            "message": f"Email sending completed. Successfully sent: {successful_sends}, Failed: {failed_sends}",
            "total_recipients": total_recipients,
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "batch_results": results
        }
    
    except smtplib.SMTPAuthenticationError:
        logger.error("SMTP authentication failed")
        raise HTTPException(status_code=401, detail="SMTP authentication failed. Please check your credentials.")
    except smtplib.SMTPException as e:
        logger.error(f"SMTP error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SMTP error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)














<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

