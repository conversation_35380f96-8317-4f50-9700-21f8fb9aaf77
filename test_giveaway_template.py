#!/usr/bin/env python3
"""
Test script for the new HORIZON_GIVEAWAY template
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"

def test_giveaway_template():
    """Test the HORIZON_GIVEAWAY template"""
    print("🎉 Testing HORIZON_GIVEAWAY Template")
    print("=" * 50)
    
    # Test template verification
    print("\n1. Verifying HORIZON_GIVEAWAY template...")
    try:
        response = requests.get(f"{BASE_URL}/verify-html-template?template_type=horizon_giveaway")
        if response.status_code == 200:
            result = response.json()
            print("✅ Template verification successful!")
            print(f"   Template size: {result.get('template_size')} characters")
            print(f"   Preview: {result.get('template_preview')}")
        else:
            print(f"❌ Template verification failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error verifying template: {e}")
        return False
    
    # Test sending giveaway email
    print("\n2. Sending HORIZON_GIVEAWAY email...")
    
    email_data = {
        "to_emails": TEST_EMAIL,
        "subject": "🎉 EXCLUSIVE GIVEAWAY - Win Up to $10,000 Funded Account!",
        "message": "",
        "email_type": "html",
        "template_type": "horizon_giveaway"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/send-email", json=email_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Giveaway email sent successfully!")
            print(f"   Campaign ID: {result.get('campaign_id', 'N/A')}")
            print(f"   Tracking enabled: {result.get('tracking_enabled', False)}")
            print(f"   Recipients: {result.get('total_recipients')}")
            print(f"   Successful sends: {result.get('successful_sends')}")
            
            campaign_id = result.get('campaign_id')
            if campaign_id:
                print(f"   Analytics URL: {BASE_URL}/analytics/campaign/{campaign_id}")
                return campaign_id
        else:
            print(f"❌ Failed to send giveaway email: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Error sending giveaway email: {e}")
    
    return None

def test_giveaway_analytics(campaign_id):
    """Test analytics for the giveaway campaign"""
    if not campaign_id:
        print("\n⚠️  No campaign ID available for analytics testing")
        return
    
    print(f"\n3. Testing giveaway campaign analytics...")
    
    try:
        response = requests.get(f"{BASE_URL}/analytics/campaign/{campaign_id}")
        if response.status_code == 200:
            analytics = response.json()
            print("✅ Giveaway Campaign Analytics:")
            print(f"   Campaign Name: {analytics.get('campaign_name')}")
            print(f"   Total Recipients: {analytics.get('total_recipients')}")
            print(f"   Total Clicks: {analytics.get('total_clicks')}")
            print(f"   Unique Clickers: {analytics.get('unique_clickers')}")
            print(f"   Click Rate: {analytics.get('click_rate')}%")
            
            click_stats = analytics.get('click_stats_by_button', {})
            if click_stats:
                print("   Click Stats by Button:")
                for button_type, clicks in click_stats.items():
                    print(f"     - {button_type}: {clicks} clicks")
            else:
                print("   No clicks recorded yet")
        else:
            print(f"❌ Failed to get giveaway analytics: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting giveaway analytics: {e}")

def show_giveaway_features():
    """Show the features of the HORIZON_GIVEAWAY template"""
    print("\n4. HORIZON_GIVEAWAY Template Features:")
    print("=" * 50)
    
    features = [
        "🎨 Eye-catching gradient background (orange to gold)",
        "🏆 Grand prize giveaway design with emojis",
        "🎁 Clear prize structure display",
        "📋 Step-by-step entry instructions",
        "🔐 Multiple CTA buttons (Enter Giveaway, Login, Learn More)",
        "⏰ Urgency messaging with countdown feel",
        "📱 Social media integration links",
        "🌐 Branded website link",
        "📊 Full click tracking support",
        "📧 Professional email layout with shadows and gradients"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n   Tracked CTA Buttons:")
    print("   - 🎯 ENTER GIVEAWAY NOW! → https://www.fundedhorizon.com")
    print("   - 🔐 LOGIN TO ENTER → https://www.fundedhorizon.com/login")
    print("   - 📖 LEARN MORE → https://www.fundedhorizon.com")
    print("   - 🌐 Visit FundedHorizon.com → https://www.fundedhorizon.com")
    print("   - Social media links (Facebook, Twitter, Instagram, Telegram)")

def main():
    """Main test function"""
    print("🚀 HORIZON_GIVEAWAY Template Test Suite")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    
    # Test server connection
    try:
        response = requests.get(f"{BASE_URL}/analytics/summary")
        if response.status_code != 200:
            print("⚠️  Server connection test failed")
            print("Make sure to run: python main.py")
            return
    except Exception as e:
        print(f"⚠️  Could not connect to server: {e}")
        print("Make sure to run: python main.py")
        return
    
    # Run tests
    campaign_id = test_giveaway_template()
    test_giveaway_analytics(campaign_id)
    show_giveaway_features()
    
    print("\n" + "=" * 50)
    print("🎉 HORIZON_GIVEAWAY Template Test Complete!")
    print("\nNext steps:")
    print("1. Check your email inbox for the giveaway email")
    print("2. Click on the CTA buttons to test tracking")
    print("3. Check analytics to see click data")
    print(f"4. Visit: {BASE_URL}/analytics/summary")
    
    if campaign_id:
        print(f"5. Giveaway campaign analytics: {BASE_URL}/analytics/campaign/{campaign_id}")
    
    print("\n📧 To send giveaway emails to your list:")
    print('curl -X POST http://localhost:8000/send-email \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"to_emails": "<EMAIL>", "subject": "🎉 WIN BIG - Exclusive Giveaway!", "email_type": "html", "template_type": "horizon_giveaway"}\'')

if __name__ == "__main__":
    main()
