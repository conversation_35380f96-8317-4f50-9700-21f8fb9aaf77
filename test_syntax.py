#!/usr/bin/env python3
"""
Quick syntax test for the main module
"""

try:
    import main
    print("✅ Main module imported successfully")
    
    # Test database initialization
    print("✅ Database initialized")
    
    # Test URL replacement function
    test_html = '<a href="https://www.fundedhorizon.com">Test</a>'
    result = main.replace_urls_with_tracking(test_html, "test-recipient", "test-campaign")
    print(f"✅ URL replacement works: {len(result) > len(test_html)}")
    
    print("✅ All basic functions work correctly")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
